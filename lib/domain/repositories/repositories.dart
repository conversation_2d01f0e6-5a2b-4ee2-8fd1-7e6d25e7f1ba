/// Domain Repositories Barrel Export
/// Repository interfaces defining data access contracts
/// 
/// This file provides a single import point for all repository interfaces.
/// Repositories define the contracts for data access without implementation details.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/repositories/repositories.dart';
/// 
/// // Now you can use any repository interface:
/// class AuthUseCase {
///   final AuthRepository _authRepository;
///   
///   AuthUseCase(this._authRepository);
///   
///   Future<Either<Failure, User>> login(String email, String password) {
///     return _authRepository.login(email, password);
///   }
/// }
/// ```
/// 
/// ## Repository Categories:
/// - **Auth Repositories**: Authentication and user management
library repositories;

export 'auth_repository.dart';
