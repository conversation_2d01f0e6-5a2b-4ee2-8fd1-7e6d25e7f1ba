/// Domain Services Barrel Export
/// Service interfaces defining business logic contracts
/// 
/// This file provides a single import point for all domain service interfaces.
/// Services define contracts for business logic and external integrations.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/services/services.dart';
/// 
/// // Now you can use any service interface:
/// class MyUseCase {
///   final LoggerService _logger;
///   final NetworkInfo _networkInfo;
///   final StorageService _storage;
///   
///   MyUseCase(this._logger, this._networkInfo, this._storage);
///   
///   Future<void> performAction() async {
///     _logger.logInfo('Starting action');
///     
///     if (await _networkInfo.isConnected) {
///       // Perform network operation
///     }
///     
///     await _storage.saveData('key', 'value');
///   }
/// }
/// ```
/// 
/// ## Service Categories:
/// - **Infrastructure Services**: Database, logging, storage
/// - **Network Services**: Network monitoring, connectivity
/// - **Environment Services**: Environment configuration
library services;

export 'database_service.dart';
export 'environment_service.dart';
export 'logger_service.dart';
export 'network_info.dart';
export 'network_monitoring_service.dart';
export 'storage_service.dart';
