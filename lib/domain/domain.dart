/// Domain Layer Master Barrel Export
/// Single import point for all domain layer components
/// 
/// This file provides centralized access to all domain layer components:
/// - Entities (business objects with domain logic)
/// - Repositories (data access contracts)
/// - Services (business logic and external integration contracts)
/// 
/// The domain layer represents the core business logic and is independent
/// of external frameworks, UI, and data sources.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/domain.dart';
/// 
/// // Access any domain component:
/// 
/// // Entities
/// final user = User(id: '123', name: 'John');
/// final env = EnvironmentEntity(name: 'dev', baseUrl: 'https://api.dev');
/// 
/// // Repository interfaces
/// class AuthUseCase {
///   final AuthRepository authRepository;
///   AuthUseCase(this.authRepository);
/// }
/// 
/// // Service interfaces
/// class NetworkUseCase {
///   final NetworkInfo networkInfo;
///   final LoggerService logger;
///   NetworkUseCase(this.networkInfo, this.logger);
/// }
/// ```
/// 
/// ## Domain Layer Organization:
/// - **entities/**: Business objects containing domain logic
/// - **repositories/**: Contracts for data access (implemented in data layer)
/// - **services/**: Contracts for business logic and external integrations
library domain;

export 'entities/entities.dart';
export 'repositories/repositories.dart';
export 'services/services.dart';
