/// Core Layer Master Barrel Export
/// Single import point for all core functionality
/// 
/// This file provides centralized access to all core layer components:
/// - Constants (API, app, environment configurations)
/// - Utils (logging, responsive helpers)
/// - Enums (application-wide enumerations)
/// - Error handling (failures, error types, codes)
/// - Services (core service interfaces)
/// - Theme (app theme and colors)
/// - Router (navigation routes)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/core.dart';
/// 
/// // Access any core functionality:
/// 
/// // Constants
/// final timeout = ApiConstants.defaultTimeoutDuration;
/// final padding = AppDimens.paddingLarge;
/// 
/// // Utils
/// AppLogger.info('Application started');
/// if (AppResponsive.isMobile(context)) { /* mobile code */ }
/// 
/// // Enums
/// final status = NetworkConnectionStatus.connected;
/// 
/// // Error handling
/// return Left(ServerFailure(ServerErrorType.timeout));
/// 
/// // Theme
/// final primaryColor = AppColors.primaryColor;
/// ```
/// 
/// ## Core Layer Organization:
/// - **constants/**: All application constants and configuration
/// - **utils/**: Utility classes and helper functions
/// - **enums/**: Application-wide enumerations
/// - **error/**: Error handling, failures, and error types
/// - **services/**: Core service interfaces
/// - **theme/**: Application theme and styling
/// - **router/**: Navigation and routing configuration
library core;

// Constants - all app constants and configuration
export 'constants/constants.dart';

// Utils - utility classes and helpers
export 'utils/utils.dart';

// Enums - application-wide enumerations
export 'enums/document_side.dart';
export 'enums/network_connection_status.dart';

// Error handling - failures, error types, and codes
export 'error/error_types.dart';
export 'error/failures.dart';
export 'error/error_codes.dart';

// Services - core service interfaces
export 'services/image_picker_service.dart';

// Theme - app theme and colors
export 'theme/app_theme.dart';
export 'theme/app_color.dart';

// Router - navigation routes
export 'router/app_routes.dart';
