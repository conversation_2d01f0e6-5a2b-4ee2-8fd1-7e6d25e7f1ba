/// Core Utils Barrel Export
/// Centralized access to all utility classes and helper functions
/// 
/// This file provides a single import point for all utility classes used throughout the app.
/// Import this file instead of importing individual utility files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/utils/utils.dart';
/// 
/// // Now you can use any utility:
/// AppLogger.info('Message');
/// 
/// // Use responsive extensions:
/// Widget myWidget = Container().responsivePadding(all: 16);
/// 
/// // Check device type:
/// if (AppResponsive.isMobile(context)) {
///   // Mobile specific code
/// }
/// ```
library utils;

export 'app_logger.dart';
export 'app_responsive.dart';
