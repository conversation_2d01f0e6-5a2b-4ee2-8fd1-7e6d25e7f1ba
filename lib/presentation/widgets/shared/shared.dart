/// Shared Widgets Barrel Export
/// Cross-feature widgets used by multiple features but not globally
/// 
/// This file provides a single import point for widgets that are shared between
/// 2-3 features but are not used globally throughout the entire app.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/widgets/shared/shared.dart';
/// 
/// // Now you can use any shared widget:
/// UserAvatar(
///   imageUrl: user.avatarUrl,
///   size: 50,
/// )
/// 
/// LoadingOverlay(
///   isLoading: isLoading,
///   child: YourContent(),
/// )
/// ```
/// 
/// ## Widget Categories:
/// - **User Components**: UserAvatar for displaying user profile images
/// - **Overlay Components**: LoadingOverlay for loading states
library shared_widgets;

export 'loading_overlay.dart';
export 'user_avatar.dart';
