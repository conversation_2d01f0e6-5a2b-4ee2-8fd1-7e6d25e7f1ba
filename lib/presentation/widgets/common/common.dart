/// Common Widgets Barrel Export
/// Reusable widgets used across the entire application
/// 
/// This file provides a single import point for all common widgets that are used
/// throughout multiple features and screens in the app.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/widgets/common/common.dart';
/// 
/// // Now you can use any common widget:
/// CommonButton(
///   text: 'Login',
///   onPressed: () {},
/// )
/// 
/// CommonTextField(
///   label: 'Email',
///   controller: emailController,
/// )
/// 
/// CommonDropdown<String>(
///   items: ['Option 1', 'Option 2'],
///   onChanged: (value) {},
/// )
/// ```
/// 
/// ## Widget Categories:
/// - **Base Components**: BaseScreen for consistent screen structure
/// - **Form Components**: CommonTextField, CommonDropdown, CommonButton
/// - **Layout Components**: BottomButton for consistent bottom actions
/// - **Navigation Components**: CustomAppBar for consistent app bar
/// - **Utility Components**: PhotoSourcePicker for image selection
library common_widgets;

export 'base_screen.dart';
export 'bottom_button.dart';
export 'common_button.dart';
export 'common_dropdown.dart';
export 'common_text_field.dart';
export 'custom_app_bar.dart';
export 'photo_source_picker.dart';
