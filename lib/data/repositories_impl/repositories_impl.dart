/// Repository Implementations Barrel Export
/// Concrete implementations of domain repository interfaces
/// 
/// This file provides a single import point for all repository implementations.
/// These implementations handle data access logic and coordinate between
/// remote and local data sources.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/data/repositories_impl/repositories_impl.dart';
/// 
/// // Repository implementations are typically registered in DI:
/// @module
/// abstract class RepositoryModule {
///   @Injectable(as: AuthRepository)
///   AuthRepositoryImpl authRepository(
///     AuthApiService apiService,
///     UserLocalDataSource localDataSource,
///   ) => AuthRepositoryImpl(apiService, localDataSource);
/// }
/// ```
/// 
/// ## Repository Implementation Categories:
/// - **Auth Repositories**: Authentication and user management implementations
library repositories_impl;

export 'auth_repository_impl.dart';
