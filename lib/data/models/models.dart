/// Data Models Master Barrel Export
/// All data models and DTOs for the application
/// 
/// This file provides a single import point for all data models including:
/// - Remote API models (DTOs)
/// - Local storage models
/// - Generated models (Freezed, JSON serializable)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/data/models/models.dart';
/// 
/// // Access any data model:
/// 
/// // Remote API models
/// final userModel = UserModel(
///   id: '123',
///   name: '<PERSON>',
///   email: '<EMAIL>',
/// );
/// 
/// // Local storage models
/// final userLocal = UserLocalModel(
///   id: 1,
///   userId: '123',
///   name: '<PERSON>',
/// );
/// 
/// // Convert between models and entities
/// final userEntity = userModel.toEntity();
/// final modelFromEntity = UserModel.fromEntity(userEntity);
/// ```
/// 
/// ## Model Categories:
/// - **Remote Models**: API response/request models (DTOs)
/// - **Local Models**: Database and local storage models
library models;

// Local models - database and local storage
export 'local/local.dart';

// Remote models - API DTOs
export 'user_model.dart';
