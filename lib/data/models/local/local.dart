/// Local Data Models Barrel Export
/// Models for local data storage (database, cache, etc.)
/// 
/// This file provides a single import point for all local data models.
/// Local models are used for data persistence and local storage.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/data/models/local/local.dart';
/// 
/// // Now you can use any local model:
/// final userLocal = UserLocalModel(
///   id: 1,
///   userId: '123',
///   name: '<PERSON>',
///   email: '<EMAIL>',
/// );
/// ```
/// 
/// ## Local Model Categories:
/// - **User Models**: Local user data models for database storage
library local_models;

export 'user_local_model.dart';
